import { makeAutoObservable, set } from 'mobx';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { TFurnitureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity';
import { TBaseGroupEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity';
import { TBaseEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity';
import { TViewCameraEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity';
import { AIGCService } from '@/Apps/LayoutAI/Services/AIGC/AIGCService';
import { RenderReqOffline } from '@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline';
interface spaceData {
  // 参数还没定义
}

interface position {
  top: string,
  left: string

}
/**
 * @description 主页数据
 */

interface Size {
  type: string,
  visible: boolean,
}

class HomeStore {
  singleSpaceData: spaceData = {};
  appOpenAsCadPlugin = false;
  ismoveCanvas = true;
  showCommandBar = false; // 是否显示小黑条
  position = {
    top: '0px',
    left: '0px'
  }
  selectData: { rooms: TRoom[], clickOnRoom: boolean } = { rooms: [], clickOnRoom: false };
  currenScheme = {} as any;
  roomInfos = [] as any;
  room2SeriesSampleArray = [] as any;
  selectedRoom = null as TRoomEntity;
  selectEntity = {} as TFurnitureEntity | TBaseGroupEntity | TRoomEntity | any;
  showReplace = false;
  designMode = 'AiCadMode' as string;
  isShowWallTopMenu = false as boolean;
  showWelcomePage = false as boolean;
  showMySchemeList = false as boolean;
  key = 0 as number;
  showDreamerPopup = false as boolean;
  initialDistance = 0;
  scale = 0.1;
  img_base64 = '';
  showSaveLayoutSchemeDialog = {show: false, source: ''};  // 保存方案弹窗
  shareVisible = false as boolean;
  sizeInfo = {} as Size;
  roomEntities = [] as any;
  attribute = {} as any;
  menu_Key = '' as string;
  viewMode = '2D' as string;  // 当前移动端模式
  currentAIDrawImageID = '' as string;
  refreshAtlas = false as boolean;
  aiDrawLoad = false as boolean;  // 是否加载AI绘制弹窗
  showAiDraw = false as boolean;   // 是否显示AI绘制弹窗
  isSingleRoom = true as boolean;  // 是否是单个房间
  IsLandscape = false as boolean;   // 是否是横屏
  openFilterField = false as boolean;
  showButton = true as boolean;
  guideMapCurrentRoom = null as TRoom;
  zIndexOf3DViewer = 0 as number;
  showAtlas = false as boolean;
  preview3D = false as boolean;
  isShowRightSide = false as boolean;
  isShowAppScene = false as boolean;
  isShowMaterialPanel = false as boolean;
  isshowRoomInfo = false as boolean;
  sunDEnterOpen = false as boolean;    //选套系弹窗

  isdrawPicture = false as boolean; // 是否绘制图片, 标记是否点击渲染出图按钮
  drawPictureMode = 'render' as string; // 渲染出图模式---AI绘图aiDrawing、标准渲染render、全景渲染panoRender
  aspectRatioMode = 5 as number // 渲染出图比例 1=>4/3  2->16:9  3->3:4  4->9:16 5->原图
  atlasMode = 'render' as string; // 图册模式---AI绘图aidraw、标准渲染render、全景渲染panoRender
  showHouseSchemeAddForm = false as boolean;
  currentViewCameras = [] as TViewCameraEntity[];
  showEnterPage = {show: false, source: ''};   // pad端是否显示进入流程的弹窗
  showSubmitInfo = false as boolean; // 是否显示提交信息弹窗
  showCabinetCompute = false as boolean; // 是否显示柜体计算弹窗
  isAutoExit = '' as string; // 是否自动退出 1=>是 2=>否

  genCount = 0 as number | null // “生成中”的图片总数
  // ai_genCount = 0 as number // “生成中”的ai绘图总数
  // render_genCount = 0 as number // “生成中”的ai绘图总数
  isShowHouseDetail = {show: false, source: ''}; // 是否显示梦想家AI布局的户型详情弹窗
  showStartPage = {show: false, source: ''}

  tmp = null as any
  setTmp(data:any){
    this.tmp = data
  }

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }
  setShowStartPage(data: {show: boolean, source: string}){
    this.showStartPage = data
  }
  setIsShowHouseDetail(data: {show: boolean, source: string}) {
    this.isShowHouseDetail = data;
  }
  setIsdrawPicture(data: boolean) {
    this.isdrawPicture = data;
  }
  setDrawPictureMode(data: string) {
    this.drawPictureMode = data;
  }
  setAspectRatioMode(data: number) {
    this.aspectRatioMode = data;
  }
  setShowButton(data: boolean) {
    this.showButton = data;
  }
  // action
  setUserInfo(data: spaceData) {
    this.singleSpaceData = data;
  }

  setAppOpenAsCadPlugin(data: boolean) {
    this.appOpenAsCadPlugin = data;
  }

  setIsmoveCanvas(data: boolean) {
    this.ismoveCanvas = data;
  }

  setShowCommandBar(data: boolean) {
    this.showCommandBar = data;
  }

  setPosition(data: position) {
    this.position = data;
  }
  setSelectData(data: { rooms: TRoom[], clickOnRoom: boolean }) {
    this.selectData = data;
  }
  setCurrenScheme(data: any) {
    this.currenScheme = data;
  }
  setRoomInfos(data: any) {
    this.roomInfos = data;
  }
  setRoom2SeriesSampleArray(data: any) {
    this.room2SeriesSampleArray = data;
  }
  setSelectedRoom(data: TRoomEntity) {
    this.selectedRoom = data;
  }
  setShowReplace(data: boolean) {
    this.showReplace = data;
  }
  setSelectEntity(data: TBaseEntity) {
    this.selectEntity = data;
  }
  setDesignMode(data: string) {
    this.designMode = data;
  }
  setIsShowWallTopMenu(data: boolean) {
    this.isShowWallTopMenu = data;
  }
  setShowWelcomePage(data: boolean) {
    this.showWelcomePage = data;
  }
  setShowMySchemeList(data: boolean) {
    this.showMySchemeList = data;
  }
  setKey(data: number) {
    this.key = data;
  }
  setShowDreamerPopup(data: boolean) {
    this.showDreamerPopup = data;
  }
  setInitialDistance(data: number) {
    this.initialDistance = data;
  }
  setScale(data: number) {
    this.scale = data;
  }
  setImgBase64(data: string) {
    this.img_base64 = data;
  }
  /**
   * @description 设置保存方案弹窗
   * @param data 
   * show: 是否显示
   * source: exitBtn来源影响保存文案：例如直接退出或退出，影响设置store.homeStore.setIsAutoExit('autoExit');
   */
  setShowSaveLayoutSchemeDialog(data: {show: boolean, source: string}) {
    this.showSaveLayoutSchemeDialog = data;
  }
  setShareVisible(data: boolean) {
    this.shareVisible = data;
  }
  setSizeInfo(data: Size) {
    this.sizeInfo = data;
  }
  setRoomEntites(data: any) {
    this.roomEntities = data;
  }

  setAttribute(data: any) {
    this.attribute = data;
  }
  setMenuKey(data: string) {
    this.menu_Key = data;
  }
  setViewMode(data: string) {
    this.viewMode = data;
  }
  setCurrentAIDrawImageID(data: any) {
    this.currentAIDrawImageID = data;
  }
  setRefreshAtlas(data: boolean) {
    this.refreshAtlas = data;
  }
  setAiDrawLoad(data: boolean) {
    this.aiDrawLoad = data;
  }
  setShowAiDraw(data: boolean) {
    this.showAiDraw = data;
  }
  setIsSingleRoom(data: boolean) {
    this.isSingleRoom = data;
  }
  setIsLandscape(data: boolean) {
    this.IsLandscape = data;
  }
  setOpenFilterField(data: boolean) {
    this.openFilterField = data;
  }
  setGuideMapCurrentRoom(data: TRoom) {
    this.guideMapCurrentRoom = data;
  }
  setZIndexOf3DViewer(data: number) {
    this.zIndexOf3DViewer = data;
  }
  setShowAtlas(data: boolean) {
    this.showAtlas = data;
  }
  setAtlasMode(data: string) {
    this.atlasMode = data;
  }  
  setPreview3D(data: boolean) {
    this.preview3D = data;
  }

  setIsShowRightSide(data: boolean) {
    this.isShowRightSide = data;
  }
  setIsShowAppScene(data: boolean) {
    this.isShowAppScene = data;
  }
  setIsShowMaterialPanel(data: boolean) {
    this.isShowMaterialPanel = data;
  }
  setIsShowRoomInfo(data: boolean) {
    this.isshowRoomInfo = data;
  }
  setSunDEnterOpen(data: boolean) {
    this.sunDEnterOpen = data;
  }
  setShowHouseSchemeAddForm(data: boolean) {
    this.showHouseSchemeAddForm = data;
  }
  setCurrentViewCameras(data: TViewCameraEntity[]) {
    this.currentViewCameras = data;
  }
  /**
   * @description 设置进入流程的弹窗
   * @param data 
   * show: 是否显示
   * source: 来源, exitBtn=>退出按钮, enterpage=>进入流程
   */
  setShowEnterPage(data: {show: boolean, source: string}) {
    this.showEnterPage = data;
  }
  setShowSubmitInfo(data: boolean) {
    this.showSubmitInfo = data;
  }
  setShowCabinetCompute(data: boolean) {
    this.showCabinetCompute = data;
  }
  /**
   * @description 设置是否自动退出
   * @param data 
   * data: 保存方案后是否自动退出, autoExit=>自动退出到首页或者关闭iframe, null=>不自动退出
   */
  setIsAutoExit(data: string) {
    this.isAutoExit = data;
  }

  setGenCount(data: number | null){
    this.genCount = data
  }
  // setAiGenCount(data: number){
  //   this.genCount = data
  // }
  // setRenderGenCount(data: number){
  //   this.genCount = data
  // }
  async query_aiScheme(){
    let res = await AIGCService.instance.queryImageList('');
    if(res && res.result){
      // 找到state为0的
      const count = res.result.filter((item: {state: number}) => item.state === 0).length;
      return count
    }
    return null
  }
  async query_renderScheme(){
    let res = await RenderReqOffline.instance.requestAtlas({
          userId: '', // 用户ID
          flag: '', // 任务类型
          schemeId: '', // 方案ID
          pageIndex: 1,
          pageSize: 15,
          all: '', // 标志
          isRtx: 0, // RTX 标识
          resolutionTag: '', // 分辨率标签
          type: 0, // 标识类型
          exclusiveRealAdjustLight: false, // 排除实时调光数据
          authCode: ''
      }
    );
    if(res && res.success){
      // 找到status为0（等待）和1（渲染中）的
      const count = res.res.data?.ReturnList?.filter((item: { Status: number; }) => 
        item.Status === 1 || item.Status === 0
      ).length;   
      return count
    }
    return null
  }
  async query_genCount(){
    const a = await this.query_aiScheme()
    const r = await this.query_renderScheme()
    if(a !== null && r !== null){     
      this.setGenCount(a + r)
      return
    }
    this.setGenCount(null)
  }
}

export default HomeStore;